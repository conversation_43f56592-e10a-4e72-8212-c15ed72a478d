﻿import os
import re
from enum import Enum

from tempfile import NamedTemporaryFile
from typing import Optional

from graia.ariadne.message.element import Plain, Voice, Image
from loguru import logger

from constants import config
from utils.azure_tts import synthesize_speech, encode_to_silk

tts_voice_dic = {}
"""各引擎的音色列表"""

from log_config import setup_logger
logger = setup_logger()


def remove_action_descriptions(text: str) -> str:
    """
    移除文本中的动作描述（括号内的内容）
    支持中文括号（）和英文括号()，包括嵌套括号

    Args:
        text: 原始文本

    Returns:
        str: 移除动作描述后的文本
    """
    if not text:
        return text

    # 使用栈来处理嵌套括号
    result = []
    bracket_depth = 0

    # 定义开括号和闭括号
    open_brackets = {'(', '（'}
    close_brackets = {')', '）'}

    for char in text:
        if char in open_brackets:
            bracket_depth += 1
        elif char in close_brackets:
            if bracket_depth > 0:
                bracket_depth -= 1
            else:
                # 没有匹配的开括号，保留这个闭括号
                result.append(char)
        elif bracket_depth == 0:
            # 不在括号内，保留字符
            result.append(char)
        # 在括号内的字符被忽略

    # 合并结果并清理空格
    cleaned_text = ''.join(result)
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

    logger.debug(f"[TTS文本预处理] 原文: {text}")
    logger.debug(f"[TTS文本预处理] 处理后: {cleaned_text}")

    return cleaned_text


class VoiceType(Enum):
    Wav = "wav"
    Mp3 = "mp3"
    Silk = "silk"


class TtsVoice:

    def __init__(self):
        self.engine = None
        """参考：edge, azure, vits"""
        self.gender = None
        """参考：Male, Female"""
        self.full_name = None
        """参考：zh-CN-liaoning-XiaobeiNeural, af-ZA-AdriNeural, am-ET-MekdesNeural"""
        self.lang = None
        """参考：zh, af, am"""
        self.region = None
        """参考：CN, ZA, ET"""
        self.name = None
        """参考：XiaobeiNeural, AdriNeural, MekdesNeural"""
        self.alias = None
        """参考：xiaobei, adri, mekdes"""
        self.sub_region = None
        """参考：liaoning"""

    def description(self):
        return f"{self.alias}: {self.full_name}{f' - {self.gender}' if self.gender else ''}"

    @staticmethod
    def parse(engine, voice: str, gender=None):
        tts_voice = TtsVoice()
        tts_voice.engine = engine
        tts_voice.full_name = voice
        tts_voice.gender = gender
        if engine in ["edge", "azure"]:
            """如：zh-CN-liaoning-XiaobeiNeural、uz-UZ-SardorNeural"""
            voice_info = voice.split("-")
            if len(voice_info) < 3:
                return None
            lang = voice_info[0]
            region = voice_info[1]
            if len(voice_info) == 4:
                sub_region = voice_info[2]
                name = voice_info[3]
            else:
                sub_region = None
                name = voice_info[2]
            alias = name.replace("Neural", "").lower()
            tts_voice.lang = lang
            tts_voice.region = region
            tts_voice.name = name
            tts_voice.alias = alias
            tts_voice.sub_region = sub_region
        else:
            tts_voice.lang = voice
            tts_voice.alias = voice

        return tts_voice

import subprocess

async def get_paddlespeech_audio(text: str, output_file: str) -> bool:
    
    # from paddlespeech.server.bin.paddlespeech_client import TTSOnlineClientExecutor
    # import json

    # executor = TTSOnlineClientExecutor()
    # executor(
    #     input=text,
    #     server_ip="127.0.0.1",
    #     port=8091,
    #     protocol="http",
    #     spk_id=0,
    #     output=output_file,
    #     play=False)
    # conda_path = "C:\Anaconda\condabin\conda.bat"
    conda_path = "C:\ProgramData\Miniconda3\condabin\conda.bat"
    # p = subprocess.Popen(f"{conda_path} init cmd.exe", shell=True)
    
    out_wav = output_file[0:-5]+".wav"
    out_pcm = output_file[0:-5]+".pcm"
    
    p = subprocess.Popen(f"{conda_path} activate base && paddlespeech_client tts --server_ip 127.0.0.1 --port 8090 --input \"{text}\" --output {out_wav}", shell=True)
    stdout, stderr = p.communicate()
    res = p.wait()
    print("conda activate base")
    # print("运行结果：", res)
    if res == 0:
        print("运行成功")
    else:
        print("运行失败")
        return False
    
    # conda_path = "C:\\ProgramData\\Miniconda3\\Scripts\\conda.exe"
    python_file_path = "C:\\Windows-quickstart-go-cqhttp-refs.tags.v1cyx\\Windows-quickstart-go-cqhttp-refs.tags.v2.5.1\\chatgpt\\utils\\wav2silk.py"
    a1 = out_wav
    a2 = out_pcm
    a3 = output_file

    # command = f'{conda_path} activate paddlespeech && python {python_file_path} {a1} {a2} {a3}'
    command = f'{conda_path} activate base && python {python_file_path} {a1} {a2} {a3}'
    p2 = subprocess.Popen(command, shell=True)

    # p2 = subprocess.Popen(f"{conda_path} activate base && C:\\Windows-quickstart-go-cqhttp-refs.tags.v1cyx\\Windows-quickstart-go-cqhttp-refs.tags.v2.5.1\\chatgpt\\utils\\wav2silk.py", shell=True)
    stdout, stderr = p2.communicate()
    res = p2.wait()
    # p = subprocess.Popen(f"{conda_path} deactivate", shell=True)
    # res = p.wait()
    # print("conda deactivate")
    # print("运行结果：", res)
    if res == 0:
        print("运行成功")
    else:
        print("运行失败")
        # return False
    return p2.returncode == 0

class TtsVoiceManager:
    """tts音色管理"""

    @staticmethod
    def parse_tts_voice(tts_engine, voice_name) -> TtsVoice:
        if tts_engine != "edge":
            # todo support other engines
            return TtsVoice.parse(tts_engine, voice_name)
        from utils.edge_tts import edge_tts_voices
        if "edge" not in tts_voice_dic:
            tts_voice_dic["edge"] = edge_tts_voices
        _voice_dic = tts_voice_dic["edge"]
        if _voice := TtsVoice.parse(tts_engine, voice_name):
            return _voice_dic.get(_voice.alias, None)
        if voice_name in _voice_dic:
            return _voice_dic[voice_name]

    @staticmethod
    async def list_tts_voices(tts_engine, voice_prefix):
        """获取可用哪些音色"""

        def match_voice_prefix(full_name):
            if isinstance(voice_prefix, str):
                return full_name.startswith(voice_prefix)
            if isinstance(voice_prefix, list):
                for _prefix in voice_prefix:
                    if full_name.startswith(_prefix):
                        return True
                return False

        if tts_engine == "edge":
            from utils.edge_tts import load_edge_tts_voices
            if "edge" not in tts_voice_dic:
                tts_voice_dic["edge"] = await load_edge_tts_voices()
            _voice_dic = tts_voice_dic["edge"]
            return [v for v in _voice_dic.values() if voice_prefix is None or match_voice_prefix(v.full_name)]
        # todo support other engines
        return []


async def get_tts_voice(elem, conversation_context, voice_type=VoiceType.Wav) -> Optional[Voice]:
    if not isinstance(elem, Plain) or not str(elem):
        return None

    # 预处理文本：移除动作描述（括号内容）
    original_text = str(elem)
    processed_text = remove_action_descriptions(original_text)

    # 如果处理后的文本为空或只有空白字符，则不进行语音转换
    if not processed_text or not processed_text.strip():
        logger.debug(f"[TextToSpeech] 文本预处理后为空，跳过语音转换 - {conversation_context.session_id}")
        return None

    voice_suffix = f".{voice_type.value}"

    output_file = NamedTemporaryFile(mode='w+b', suffix=voice_suffix, delete=False)
    output_file.close()

    logger.debug(f"[TextToSpeech] 开始转换语音 - {conversation_context.session_id}")
    if config.text_to_speech.engine == "gptsovits":
        from utils.gptsovits_tts import gptsovits_tts_instance

        # 根据语音类型确定输出格式
        output_format = "mp3" if voice_type == VoiceType.Mp3 else "wav"

        # 使用预处理后的文本进行语音合成
        if await gptsovits_tts_instance.synthesize_speech(processed_text, output_file.name, output_format):
            logger.debug(f"[TextToSpeech] GPT-SoVITS语音转换完成 - {output_file.name} - {conversation_context.session_id}")
            voice = Voice(path=output_file.name)
            if voice_type == VoiceType.Silk:
                voice = Voice(data_bytes=await encode_to_silk(await voice.get_bytes()))
            # 标记为正常语音
            voice.is_fallback = False
            return voice
        else:
            # 如果GPT-SoVITS服务不可用，检查是否启用回退语音
            if config.text_to_speech.voice_fallback_enabled:
                logger.warning(f"[TextToSpeech] GPT-SoVITS服务不可用，使用回退语音文件")
                if await gptsovits_tts_instance.get_fallback_voice(output_file.name):
                    logger.debug(f"[TextToSpeech] 回退语音文件使用完成 - {output_file.name} - {conversation_context.session_id}")
                    voice = Voice(path=output_file.name)
                    if voice_type == VoiceType.Silk:
                        voice = Voice(data_bytes=await encode_to_silk(await voice.get_bytes()))
                    # 标记为回退语音
                    voice.is_fallback = True
                    return voice
                else:
                    logger.error(f"[TextToSpeech] 回退语音文件也不可用")
                    return None
            else:
                logger.warning(f"[TextToSpeech] GPT-SoVITS服务不可用，回退语音已禁用")
                return None
    elif config.text_to_speech.engine == "vits":
        from utils.vits_tts import vits_api_instance
        if await vits_api_instance.process_message(processed_text, output_file.name, voice_type.value):
            logger.debug(f"[TextToSpeech] 语音转换完成 - {output_file.name} - {conversation_context.session_id}")
            voice = Voice(path=output_file.name)
            voice.is_fallback = False
            return voice


    # 在适当的位置调用上述函数，例如：
    if config.text_to_speech.engine == "paddlespeech":
        print("Now:paddlespeech")
        if await get_paddlespeech_audio(processed_text, output_file.name):
            logger.debug(f"[TextToSpeech] 语音转换完成 - {output_file.name} - {conversation_context.session_id}")
            voice = Voice(path=output_file.name)
            voice.is_fallback = False
            return voice


    elif config.text_to_speech.engine == "azure":
        tts_output_file_name = (f"{output_file.name}.{VoiceType.Wav.value}"
                                if voice_type == VoiceType.Silk else output_file.name)
        if await synthesize_speech(
                processed_text,
                tts_output_file_name,
                conversation_context.conversation_voice
        ):
            voice = Voice(path=tts_output_file_name)
            if voice_type == VoiceType.Silk:
                voice = Voice(data_bytes=await encode_to_silk(await voice.get_bytes()))
            voice.is_fallback = False
            logger.debug(f"[TextToSpeech] 语音转换完成 - {output_file.name} - {conversation_context.session_id}")
            return voice
    elif config.text_to_speech.engine == "edge":
        from utils.edge_tts import edge_tts_speech
        tts_output_file_name = await edge_tts_speech(
            processed_text, conversation_context.conversation_voice, output_file.name)
        if tts_output_file_name:
            output_file.name = tts_output_file_name
            voice = Voice(path=output_file.name)
            if voice_type == VoiceType.Silk:
                voice = Voice(data_bytes=await encode_to_silk(await voice.get_bytes()))
            voice.is_fallback = False
            logger.debug(f"[TextToSpeech] 语音转换完成 - {output_file.name} - {conversation_context.session_id}")
            return voice
    else:
        raise ValueError("不存在该文字转音频引擎，请检查配置文件是否正确")
