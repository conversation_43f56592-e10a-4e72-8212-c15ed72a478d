# 动作描述过滤功能说明

## 问题描述

在文字转语音功能中，大模型回复的文字可能包含动作描述，例如：
- `(深情的望着你) 谢谢你`
- `（抬起手，帮你抚平了一下头发）好的，我会保重`
- `（沉默的看着你）`

这些括号内的动作描述在语音合成时会被朗读出来，影响用户体验。

## 解决方案

在 `chatgpt/utils/text_to_speech.py` 中添加了文本预处理功能，在进行语音合成之前自动移除动作描述。

### 核心功能

#### `remove_action_descriptions(text: str) -> str`

该函数能够：
1. 移除中文括号（）和英文括号()内的所有内容
2. 正确处理嵌套括号的情况
3. 清理多余的空格
4. 保留括号外的正常文本

#### 处理示例

| 输入文本 | 输出文本 |
|---------|---------|
| `(深情的望着你) 谢谢你` | `谢谢你` |
| `（抬起手，帮你抚平了一下头发）好的，我会保重` | `好的，我会保重` |
| `你好(微笑)世界` | `你好世界` |
| `嵌套(外层(内层)外层)测试` | `嵌套测试` |
| `（沉默的看着你）` | `` (空字符串) |

### 技术实现

1. **算法**: 使用栈算法处理嵌套括号，确保正确匹配开闭括号
2. **支持的括号类型**: 中文括号（）和英文括号()
3. **应用范围**: 所有TTS引擎（GPT-SoVITS、VITS、Azure TTS、Edge TTS、PaddleSpeech）

### 修改的文件

- `chatgpt/utils/text_to_speech.py`
  - 添加了 `remove_action_descriptions()` 函数
  - 在 `get_tts_voice()` 函数中应用文本预处理
  - 所有TTS引擎都使用预处理后的文本进行语音合成

### 特殊处理

1. **空文本检查**: 如果预处理后的文本为空或只有空白字符，则跳过语音转换
2. **日志记录**: 记录原始文本和处理后的文本，便于调试
3. **兼容性**: 不影响现有的语音功能和配置

## 使用说明

该功能自动生效，无需额外配置。当启用任何TTS引擎时，所有的语音合成都会自动应用动作描述过滤。

## 测试验证

功能已通过全面测试，包括：
- 基本括号移除
- 嵌套括号处理
- 混合括号类型
- 边缘情况处理
- 空文本处理

所有测试用例均通过验证。
